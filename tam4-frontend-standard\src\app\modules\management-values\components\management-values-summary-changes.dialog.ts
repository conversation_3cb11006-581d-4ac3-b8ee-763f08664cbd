import { CommonModule } from "@angular/common";
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SelectorMap } from '@creactives/models';
import { Tam4TranslationService } from '@creactives/tam4-translation-core';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ConfirmationService, MessageService } from "primeng/api";
import { ButtonModule } from "primeng/button";
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { Tam4SelectorConfig, TamAbstractReduxComponent } from '../../../components';
import { TamFullHeightPanelComponent } from '../../../components/tam-full-height-panel.component';
import { EventUtils } from '../../../utils';
import { ManagementValuesService } from "../management-values.service";
import { DomainValueChangesRequestItem } from "../models/management-values.types";
import { ChangesTableComponent, ChangesTableConfig } from "./shared/changes-table/changes-table.component";

const storeSelectors: Tam4SelectorConfig[] = [
];

@Component({
    selector: 'div[managementValuesSummaryChangesDialog]',
    template: `
        <div tamFullHeightPanel class="modal" contentClass="flex flex-column">
            <ng-template pTemplate="header">
                <span class="text-lg font-semibold">{{ "domainValuesManagement.summaryChanges.title" | translate }}</span>
            </ng-template>

            <changes-table
                [data]="domainChangesValue"
                [config]="tableConfig">
            </changes-table>

            <ng-template pTemplate="footer">
                <div class="flex justify-content-end gap-2">
                    <p-button
                            severity="secondary"
                            styleClass="p-button-fixed-size"
                            icon="fas fa-times"
                            [label]="'domainValuesManagement.button.buttonCancel' | translate"
                            (onClick)="doCancel($event)">
                    </p-button>
                    <p-button
                            styleClass="p-button-fixed-size"
                            icon="fas fa-check"
                            [label]="'domainValuesManagement.button.buttonSubmit' | translate"
                            (onClick)="doConfirm($event)">
                    </p-button>
                </div>
            </ng-template>
        </div>
    `,
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TranslateModule,
    TamFullHeightPanelComponent,
    ChangesTableComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./management-values-summary-changes.dialog.scss']
})

export class ManagementValuesSummaryChangesDialog extends TamAbstractReduxComponent<SelectorMap> {

    domainChangesValue: DomainValueChangesRequestItem[];
    descriptionFilter: string;
    codeFilter: string;
    showOnlyOpen = false;
    index = 0;

    tableConfig: ChangesTableConfig = {
        mode: 'summary',
        scrollHeight: 'flex',
        tableStyle: { width: '100%', height: '100%' }
    };


    constructor(protected translate: TranslateService,
                protected tamTranslate: Tam4TranslationService,
                protected store: Store,
                private service: ManagementValuesService,
                private ref: DynamicDialogRef,
                private config: DynamicDialogConfig,
                private confirmationService: ConfirmationService,
                private messageService: MessageService) {
        super(translate, tamTranslate, store, storeSelectors);

    }

    ngOnInit() {
      super.ngOnInit();
      this.domainChangesValue = this.config.data;
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
    }

    doConfirm($event: any) {
        EventUtils.stopPropagation($event);

        this.confirmationService.confirm({
          target: null,
          key: 'mainConfirmDialog',
          message: this.translate.instant('domainValuesManagement.summaryChanges.message'),
          icon: 'pi pi-exclamation-triangle',
          acceptIcon: 'fa-solid fa-check',
          rejectIcon: 'fa-solid fa-x',
          acceptLabel: this.translate.instant('domainValuesManagement.summaryChanges.confirm'),
          rejectLabel: this.translate.instant('domainValuesManagement.button.buttonCancel'),
          rejectButtonStyleClass: 'p-button-text',
          accept: () => {
              this.validateAndSaveChanges();
          },
          reject: () => {
            this.ref.close();
          }
        });
    }

    doCancel($event: any) {
      EventUtils.stopPropagation($event);
      this.ref.close();
    }

    doClose($event: any) {
        EventUtils.stopPropagation($event);
        this.ref.close(null);
    }

    private validateAndSaveChanges(): void {
        this.service.action_doValidateChanges(
            this.domainChangesValue,
            (isValid: boolean) => this.handleValidationResult(isValid),
            (error: any) => this.handleValidationError(error)
        );
    }

    private handleValidationResult(isValid: boolean): void {
        if (isValid) {
            this.service.action_doSaveChanges(this.domainChangesValue);
            this.ref.close();
        } else {
            this.showError('domainValuesManagement.summaryChanges.validationFailed')
        }
    }

    private handleValidationError(_error: any): void {
        this.showError('domainValuesManagement.summaryChanges.validationError');
    }

    private showError(messageKey: string): void {
        this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('domainValuesManagement.messages.error'),
            detail: this.translate.instant(messageKey)
        });
    }

}
