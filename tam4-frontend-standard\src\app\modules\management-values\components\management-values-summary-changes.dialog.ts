import { CommonModule } from "@angular/common";
import { HttpClientModule } from "@angular/common/http";
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SelectorMap } from '@creactives/models';
import { Tam4TranslationService } from '@creactives/tam4-translation-core';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ConfirmationService, MessageService } from "primeng/api";
import { ButtonModule } from "primeng/button";
import { DialogModule } from "primeng/dialog";
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { TableModule } from "primeng/table";
import { TagModule } from "primeng/tag";
import { Tam4SelectorConfig, TamAbstractReduxComponent } from '../../../components';
import { TamFullHeightPanelComponent } from '../../../components/tam-full-height-panel.component';
import { EventUtils } from '../../../utils';
import { DomainValuesStatusTransformPipe } from "../common/status-transform.pipe";
import { ManagementValuesService } from "../management-values.service";
import { DomainValueChangesRequestItem, ManagementValuesTabType, OperationTypes } from "../models/management-values.types";

const storeSelectors: Tam4SelectorConfig[] = [
];

@Component({
    selector: 'div[managementValuesSummaryChangesDialog]',
    template: `
        <div tamFullHeightPanel class="modal" contentClass="flex flex-column">
            <ng-template pTemplate="header">
                <span class="text-lg font-semibold">{{ "domainValuesManagement.summaryChanges.title" | translate }}</span>
            </ng-template>

            <p-table [value]="domainChangesValue"
                     sortField="changeId"
                     sortMode="single"
                     scrollHeight="flex"
                     [scrollable]="true"
                     [tableStyle]="{ width: '100%', height: '100%' }"
                     styleClass="compact-table">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="compact-header" style="width: 80px;">{{ "domainValuesManagement.summaryChanges.change" | translate }}
                        </th>
                        <th class="compact-header"
                            style="width: 150px;">{{ "domainValuesManagement.summaryChanges.attribute" | translate }}
                        </th>
                        <th class="compact-header" style="width: 120px;">{{ "domainValuesManagement.summaryChanges.type" | translate }}</th>
                        <th class="compact-header" style="width: 250px;">{{ "domainValuesManagement.summaryChanges.changes" | translate }}</th>
                    </tr>
                </ng-template>

                <ng-template pTemplate="body" let-row>
                    <tr class="compact-row">
                        <td class="compact-cell text-center">{{ row.changeId }}</td>
                        <td class="compact-cell"><span class="font-semibold">{{ row.attributeId }}</span></td>
                        <td class="compact-cell">{{ row.changeType }}</td>
                        <td class="compact-cell changes-scroll-cell">
                            @for (change of row.itemChanges; track change.id) {
                                <div class="change-item">
                                    @if (change.changeType === tabType.VALUES) {
                                        <div class="change-header">
                                            <strong>{{ "domainValuesManagement.summaryChanges.values.valueId" | translate }}</strong> {{ change.id }}
                                            <p-tag [value]="change.operationType"
                                                   [severity]="change.operationType | domainValuesStatusTransform"
                                                   styleClass="ml-2">
                                            </p-tag>
                                        </div>
                                        @if (change.operationType === operationTypeStatus.UPDATE || change.operationType === operationTypeStatus.ADD) {
                                            @for (locale of change.locales; track locale) {
                                                @if (locale.newValue !== null && locale.newValue !== "") {
                                                    <div class="locale-change">
                                                        <strong>{{ locale.language }}:</strong>
                                                        @if (locale.oldValue !== null && locale.oldValue !== "") {
                                                            <span class="old-value">{{ locale.oldValue }}</span> → <span
                                                                    class="new-value">{{ locale.newValue }}</span>
                                                        } @else {
                                                            <span class="new-value">{{ locale.newValue }}</span>
                                                        }
                                                    </div>
                                                }
                                            }
                                        }
                                    } @else {
                                        <div class="constraint-change">
                                            <div class="constraint-header"><strong>{{ change.testAttributeValue }}:</strong></div>
                                            <div class="constraint-values">
                                                @if (change.addedValidValues.length !== 0) {
                                                    <div class="added-value">
                                                        <strong style="color:black;">
                                                            {{ "domainValuesManagement.summaryChanges.constraint.addedValidValues" | translate }}</strong>
                                                        @for (addValidValue of change.addedValidValues; let i = $index; let
                                                                last = $last; track addValidValue) {
                                                            <span>{{ addValidValue }}@if (!last) {
                                                                •
                                                            }</span>
                                                        }
                                                    </div>
                                                }
                                                @if (change.removedValidValues.length !== 0) {
                                                    <div class="removed-value">
                                                        <strong style="color:black; text-decoration: none !important;">{{ "domainValuesManagement.summaryChanges.constraint.removedValidValues" | translate }}</strong>
                                                        @for (removedValidValue of change.removedValidValues; let i = $index; let
                                                                last = $last; track removedValidValue) {
                                                            <span>{{ removedValidValue }}@if (!last) {
                                                                •
                                                            }</span>
                                                        }
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                        </td>
                    </tr>
                </ng-template>
            </p-table>

            <ng-template pTemplate="footer">
                <div class="flex justify-content-end gap-2">
                    <p-button
                            severity="secondary"
                            styleClass="p-button-fixed-size"
                            icon="fas fa-times"
                            [label]="'domainValuesManagement.button.buttonCancel' | translate"
                            (onClick)="doCancel($event)">
                    </p-button>
                    <p-button
                            styleClass="p-button-fixed-size"
                            icon="fas fa-check"
                            [label]="'domainValuesManagement.button.buttonSubmit' | translate"
                            (onClick)="doConfirm($event)">
                    </p-button>
                </div>
            </ng-template>
        </div>
    `,
  standalone: true,
  imports: [
    TableModule,
    HttpClientModule,
    TagModule,
    CommonModule,
    ButtonModule,
    TranslateModule,
    DomainValuesStatusTransformPipe,
    DialogModule,
    TamFullHeightPanelComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./management-values-summary-changes.dialog.scss']
})

export class ManagementValuesSummaryChangesDialog extends TamAbstractReduxComponent<SelectorMap> {

    domainChangesValue: DomainValueChangesRequestItem[];
    descriptionFilter: string;
    codeFilter: string;
    showOnlyOpen = false;
    index = 0;

    protected readonly tabType = ManagementValuesTabType;
    protected readonly operationTypeStatus = OperationTypes;


    constructor(protected translate: TranslateService,
                protected tamTranslate: Tam4TranslationService,
                protected store: Store,
                private service: ManagementValuesService,
                private ref: DynamicDialogRef,
                private config: DynamicDialogConfig,
                private confirmationService: ConfirmationService,
                private messageService: MessageService) {
        super(translate, tamTranslate, store, storeSelectors);

    }

    ngOnInit() {
      super.ngOnInit();
      this.domainChangesValue = this.config.data;
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
    }

    doConfirm($event: any) {
        EventUtils.stopPropagation($event);

        this.confirmationService.confirm({
          target: null,
          key: 'mainConfirmDialog',
          message: this.translate.instant('domainValuesManagement.summaryChanges.message'),
          icon: 'pi pi-exclamation-triangle',
          acceptIcon: 'fa-solid fa-check',
          rejectIcon: 'fa-solid fa-x',
          acceptLabel: this.translate.instant('domainValuesManagement.summaryChanges.confirm'),
          rejectLabel: this.translate.instant('domainValuesManagement.button.buttonCancel'),
          rejectButtonStyleClass: 'p-button-text',
          accept: () => {
              this.validateAndSaveChanges();
          },
          reject: () => {
            this.ref.close();
          }
        });
    }

    doCancel($event: any) {
      EventUtils.stopPropagation($event);
      this.ref.close();
    }

    doClose($event: any) {
        EventUtils.stopPropagation($event);
        this.ref.close(null);
    }

    private validateAndSaveChanges(): void {
        this.service.action_doValidateChanges(
            this.domainChangesValue,
            (isValid: boolean) => this.handleValidationResult(isValid),
            (error: any) => this.handleValidationError(error)
        );
    }

    private handleValidationResult(isValid: boolean): void {
        if (isValid) {
            this.service.action_doSaveChanges(this.domainChangesValue);
            this.ref.close();
        } else {
            this.showError('domainValuesManagement.summaryChanges.validationFailed')
        }
    }

    private handleValidationError(error: any): void {
        this.showError('domainValuesManagement.summaryChanges.validationError');
    }

    private showError(messageKey: string): void {
        this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('domainValuesManagement.messages.error'),
            detail: this.translate.instant(messageKey)
        });
    }

}
