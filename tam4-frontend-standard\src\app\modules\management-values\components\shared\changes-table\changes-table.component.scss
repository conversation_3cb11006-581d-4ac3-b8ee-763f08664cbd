:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

// Compact table styles
.compact-table {
  ::ng-deep {
    .p-datatable-table {
      font-size: 0.875rem;
      border-collapse: separate;
      border-spacing: 0;
    }

    .p-datatable-thead > tr > th {
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      font-weight: 600;
      background-color: var(--surface-100);
      border-bottom: 1px solid var(--surface-300);
      position: sticky;
      top: 0;
      z-index: 1;
    }

    .p-datatable-tbody > tr > td {
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      border-bottom: 1px solid var(--surface-200);
      vertical-align: top;
      line-height: 1.4;
    }

    .p-datatable-tbody > tr:hover {
      background-color: var(--surface-50);
    }

    .p-datatable-scrollable-body {
      max-height: calc(100vh - 200px);
    }
  }
}

.compact-header {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-color);
}

.compact-row {
  &:hover {
    background-color: var(--surface-50);
  }
}

.compact-cell {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  vertical-align: top;
  line-height: 1.4;
}

.changes-scroll-cell {
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
}

// Change items
.change-item {
  padding: 0.5rem;
  margin-bottom: 0.75rem;
  background-color: var(--surface-0);
  border-radius: 4px;
  border: 1px solid var(--surface-200);

  &:last-child {
    margin-bottom: 0;
  }
}

.change-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;

  strong {
    color: var(--text-color);
    margin-right: 0.5rem;
  }
}

// Locale changes
.locale-change {
  margin: 0.25rem 0;
  padding: 0.25rem 0.5rem;
  background-color: var(--surface-0);
  border-radius: 3px;
  font-size: 0.8125rem;

  strong {
    color: var(--primary-color);
    margin-right: 0.25rem;
  }
}

.removed-value {
  color: var(--red-500);
  margin-right: 0.25rem;
}

.removed-value span {
  text-decoration: line-through;
}

.removed-value strong {
  color: var(--black-500);
  text-decoration: none;
}

.added-value {
  color: var(--green-600);
  font-weight: 500;
}

.old-value {
  color: var(--red-500);
  text-decoration: line-through;
  margin-right: 0.25rem;
}

.new-value {
  color: var(--green-600);
  font-weight: 500;
  margin-right: 0.25rem;
}

// Constraint changes
.constraint-change {
  margin: 0.5rem 0;
}

.constraint-header {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;

  strong {
    color: var(--text-color);
  }
}

.constraint-values {
  padding-left: 1rem;
  font-size: 0.8125rem;
}

.constraint-old,
.constraint-new {
  margin: 0.25rem 0;

  strong {
    color: var(--text-color-secondary);
    margin-right: 0.25rem;
  }

  span {
    margin-right: 0.25rem;
  }
}

.constraint-old {
  color: var(--red-600);
}

.constraint-new {
  color: var(--green-600);
}

// Responsive adjustments
@media (max-width: 768px) {
  .compact-table {
    ::ng-deep {
      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        padding: 0.375rem 0.5rem;
        font-size: 0.8125rem;
      }
    }
  }

  .change-item {
    padding: 0.375rem;
    margin-bottom: 0.5rem;
  }

  .locale-change {
    font-size: 0.75rem;
  }
}

// Expander column for expandable mode
.expander-column {
  width: 40px !important;
  max-width: 40px !important;
  padding: 0.2rem !important;
  text-align: center;
  white-space: nowrap;
}

// Utility classes
.text-center {
  text-align: center;
}
