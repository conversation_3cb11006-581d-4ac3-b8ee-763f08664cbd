import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { EventUtils } from 'src/app/utils';
import { CommonModule } from '@angular/common';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { TagModule } from 'primeng/tag';
import { TableModule } from 'primeng/table';
import { ManagementValuesService } from '../management-values.service';
import { TamFullHeightPanelComponent } from 'src/app/components/tam-full-height-panel.component';
import { ChangesTableComponent, ChangesTableConfig } from './shared/changes-table/changes-table.component';

@Component({
  template: `
    <div tamFullHeightPanel class="modal" contentClass="flex flex-column">
      <ng-template pTemplate="header">
        <span class="text-lg font-semibold">{{ "domainValuesManagement.changeHistory.modalDetails.header" | translate }}</span>
      </ng-template>
      <div class="change-info">
        <div class="change-info-row">
          <span class="label">{{ 'domainValuesManagement.changeHistory.tableHeader.attribute' | translate }}</span>
          <span class="value">{{ change.attribute }}</span>
        </div>
        <div class="change-info-row">
          <span class="label">{{ 'domainValuesManagement.changeHistory.tableHeader.createdBy' | translate }}</span>
          <span class="value">{{ change.requester }}</span>
        </div>
        <div class="change-info-row">
          <span class="label">{{ 'domainValuesManagement.changeHistory.tableHeader.date' | translate }}</span>
          <span class="value">{{ change.requestDate | date : 'yyyy-MM-dd HH:mm' }}</span>
        </div>
        <div class="change-info-row">
          <span class="label">{{ 'domainValuesManagement.changeHistory.tableHeader.status' | translate }}</span>
          <span class="value">
            <p-tag [severity]="getSeverity(change.status)" [value]="change.status"></p-tag>
          </span>
        </div>
        <div class="change-info-row">
          <span class="label">{{ 'domainValuesManagement.changeHistory.tableHeader.changeId' | translate }}</span>
          <span class="value">{{ change.changeId }}</span>
        </div>
      </div>
      <div class="row-line"></div>
      <div *ngIf="change.valuesChanges?.length > 0">
        <h5 class="mb-3">
          {{ 'domainValuesManagement.changeHistory.modalDetails.valueChangesTitle' | translate }}
        </h5>
        <changes-table
          [data]="change.valuesChanges"
          [config]="tableConfig">
        </changes-table>
      </div>
      <div *ngIf="change.constraintChanges?.length > 0">
        <h5 class="mb-3">
          {{ 'domainValuesManagement.changeHistory.modalDetails.constraintChangesTitle' | translate }}
        </h5>
        <div *ngFor="let constrchange of change.constraintChanges" class="change-card">
          <div class="change-data">
            <span class="attribute-id">{{ constrchange.id }}</span>
            <span class="attribute-value">{{ constrchange.testAttributeValueNew }}</span>
            <span>-</span>
            <span class="old-value">{{ constrchange.validValueOld }}</span>
            <span>-</span>
            <span class="new-value">{{ constrchange.validValueNew }}</span>
          </div>
        </div>
      </div>
      <ng-template pTemplate="footer">
        <div class="fixed-hf-footer flex justify-content-end pt-3 pb-3 pr-3">
          <p-button severity="secondary" styleClass="p-button-fixed-size ellipsis"
                      [label]="'domainValuesManagement.changeHistory.modalDetails.closeButton' | translate"
                      (onClick)="handleClose($event)"></p-button>
        </div>
      </ng-template>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    TableModule,
    TagModule,
    ButtonModule,
    TamFullHeightPanelComponent,
    ChangesTableComponent
  ],
  styles: [`
    .modal-header {
        border-bottom: 1px solid #ddd;
        margin-bottom: 1rem;
    }

    .modal-body {
      display: flex;
      flex-direction: column;
      height: 100%; 
      padding: 1rem;
      box-sizing: border-box;
    }

    .change-info {
        display: inline-block;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 16px 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        font-size: 14px;
        color: #333;
    }

    .change-info-row {
      display: flex;
      gap: 8px;
      margin-bottom: 6px;
    }

    .change-info-row .label {
      font-weight: 600;
      min-width: 120px;
      color: #555;
    }

    .change-info-row .value {
      flex: 1;
      color: #000;
    }

    .row-line {
      width: 100%;
      height: 1px;
      background-color: #ccc;
      margin: 20px 0;
    }

    .expander-column {
      width: 40px !important;
      max-width: 40px !important;
      padding: 0.2rem !important;
      text-align: center;
      white-space: nowrap;
    }

    .fixed-hf-footer {
      margin-top: auto;
      display: flex;
      justify-content: flex-end; 
      padding-top: 1rem;
    }

    //TEST

    .change-card {
      background: #fff;
      border: 1px solid #ccc;
      border-radius: 6px;
      padding: 1rem;
      width: 320px;
      box-shadow: 1px 1px 4px rgba(0,0,0,0.1);
      transition: background-color 0.3s ease;
      font-family: Arial, sans-serif;
    }

    .change-data {
      font-weight: 700;
      font-size: 1.2rem;
      margin-bottom: 0.7rem;
      display: flex;
      gap: 0.6rem;
      color: #333;
    }

    .attribute-id {
      background-color: #007bff;
      color: white;
      padding: 0 0.5rem;
      border-radius: 3px;
    }

    .attribute-value {
      color: #555;
      font-style: italic;
      align-self: center;
    }

    .old-value {
      color: #F00;
      text-decoration: line-through;
    }

    .new-value {
      color: #5cb85c;
      font-weight: 600;
    }
  `]
})
export class ChangeDetailsComponent implements OnInit {
  change: any;

  tableConfig: ChangesTableConfig = {
    mode: 'expandable',
    scrollHeight: '250px',
    showHeaders: true
  };


  constructor(
    public config: DynamicDialogConfig,
    private ref: DynamicDialogRef,
    private service: ManagementValuesService
  ) {
    this.change = this.config.data;
  }

  ngOnInit() {
  }

  getSeverity(value: string): "success" | "secondary" | "info" | "warning" | "danger" | "contrast" {
    return this.service.getSeverity(value);
  }

  handleClose(event$: any) {
      EventUtils.stopPropagation(event$);
      this.ref.close();
    }
}
