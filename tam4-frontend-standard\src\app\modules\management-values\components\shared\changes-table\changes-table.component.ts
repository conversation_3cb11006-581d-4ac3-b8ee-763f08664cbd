import { CommonModule } from "@angular/common";
import { ChangeDetectionStrategy, Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { TableModule } from "primeng/table";
import { TagModule } from "primeng/tag";
import { DomainValueChangesRequestItem, ManagementValuesTabType, OperationTypes } from "../../../models/management-values.types";

export interface ChangesTableData {
  changeId?: string;
  attributeId?: string;
  changeType?: ManagementValuesTabType;
  itemChanges?: any[];
  // For ChangeDetailsComponent format
  valuesChanges?: any[];
  constraintChanges?: any[];
}

export interface ChangesTableConfig {
  mode: 'summary' | 'details' | 'expandable';
  showHeaders?: boolean;
  scrollHeight?: string;
  tableStyle?: any;
  expandable?: boolean;
}

@Component({
  selector: 'changes-table',
  template: `
    <p-table [value]="processedData"
             [sortField]="config.mode === 'summary' ? 'changeId' : undefined"
             sortMode="single"
             [scrollHeight]="config.scrollHeight || 'flex'"
             [scrollable]="true"
             [tableStyle]="config.tableStyle || { width: '100%', height: '100%' }"
             styleClass="compact-table">
      
      <!-- Summary mode headers -->
      <ng-template pTemplate="header" *ngIf="config.mode === 'summary' && config.showHeaders !== false">
        <tr>
          <th class="compact-header" style="width: 80px;">{{ "domainValuesManagement.summaryChanges.change" | translate }}</th>
          <th class="compact-header" style="width: 150px;">{{ "domainValuesManagement.summaryChanges.attribute" | translate }}</th>
          <th class="compact-header" style="width: 120px;">{{ "domainValuesManagement.summaryChanges.type" | translate }}</th>
          <th class="compact-header" style="width: 250px;">{{ "domainValuesManagement.summaryChanges.changes" | translate }}</th>
        </tr>
      </ng-template>

      <!-- Details mode headers -->
      <ng-template pTemplate="header" *ngIf="config.mode === 'details' && config.showHeaders !== false">
        <tr>
          <th class="compact-header">{{ "domainValuesManagement.changeHistory.modalDetails.id" | translate }}</th>
          <th class="compact-header">{{ "domainValuesManagement.changeHistory.modalDetails.changes" | translate }}</th>
        </tr>
      </ng-template>

      <!-- Expandable mode headers -->
      <ng-template pTemplate="header" *ngIf="config.mode === 'expandable' && config.showHeaders !== false">
        <tr>
          <th class="expander-column"></th>
          <th>{{ "domainValuesManagement.changeHistory.modalDetails.id" | translate }}</th>
        </tr>
      </ng-template>

      <!-- Summary mode body -->
      <ng-template pTemplate="body" let-row *ngIf="config.mode === 'summary'">
        <tr class="compact-row">
          <td class="compact-cell text-center">{{ row.changeId }}</td>
          <td class="compact-cell"><span class="font-semibold">{{ row.attributeId }}</span></td>
          <td class="compact-cell">{{ row.changeType }}</td>
          <td class="compact-cell changes-scroll-cell">
            @for (change of row.itemChanges; track change.id) {
              <div class="change-item">
                @if (change.changeType === tabType.VALUES) {
                  <div class="change-header">
                    <strong>{{ "domainValuesManagement.summaryChanges.values.valueId" | translate }}</strong> {{ change.id }}
                    <p-tag [value]="change.operationType"
                           [severity]="getSeverity(change.operationType)"
                           styleClass="ml-2">
                    </p-tag>
                  </div>
                  @if (change.operationType === operationTypeStatus.UPDATE || change.operationType === operationTypeStatus.ADD) {
                    @for (locale of change.locales; track locale) {
                      @if (locale.newValue !== null && locale.newValue !== "") {
                        <div class="locale-change">
                          <strong>{{ locale.language }}:</strong>
                          @if (locale.oldValue !== null && locale.oldValue !== "") {
                            <span class="old-value">{{ locale.oldValue }}</span> → <span class="new-value">{{ locale.newValue }}</span>
                          } @else {
                            <span class="new-value">{{ locale.newValue }}</span>
                          }
                        </div>
                      }
                    }
                  }
                } @else {
                  <div class="constraint-change">
                    <div class="constraint-header"><strong>{{ change.testAttributeValue }}:</strong></div>
                    <div class="constraint-values">
                      @if (change.addedValidValues.length !== 0) {
                        <div class="added-value">
                          <strong style="color:black;">
                            {{ "domainValuesManagement.summaryChanges.constraint.addedValidValues" | translate }}</strong>
                          @for (addValidValue of change.addedValidValues; let i = $index; let last = $last; track addValidValue) {
                            <span>{{ addValidValue }}@if (!last) { • }</span>
                          }
                        </div>
                      }
                      @if (change.removedValidValues.length !== 0) {
                        <div class="removed-value">
                          <strong style="color:black; text-decoration: none !important;">{{ "domainValuesManagement.summaryChanges.constraint.removedValidValues" | translate }}</strong>
                          @for (removedValidValue of change.removedValidValues; let i = $index; let last = $last; track removedValidValue) {
                            <span>{{ removedValidValue }}@if (!last) { • }</span>
                          }
                        </div>
                      }
                    </div>
                  </div>
                }
              </div>
            }
          </td>
        </tr>
      </ng-template>

      <!-- Details mode body -->
      <ng-template pTemplate="body" let-row *ngIf="config.mode === 'details'">
        <tr class="compact-row">
          <td class="compact-cell">{{ row.id }}</td>
          <td class="compact-cell changes-scroll-cell">
            <div class="change-item">
              @if (row.locales && row.locales.length > 0) {
                @for (locale of row.locales; track locale) {
                  <div class="locale-change">
                    <strong>{{ locale.language }}:</strong>
                    @if (locale.oldValue !== null && locale.oldValue !== "") {
                      <span class="old-value">{{ locale.oldValue }}</span> → <span class="new-value">{{ locale.newValue }}</span>
                    } @else {
                      <span class="new-value">{{ locale.newValue }}</span>
                    }
                    <p-tag [value]="locale.operationType"
                           [severity]="getSeverity(locale.operationType)"
                           styleClass="ml-2">
                    </p-tag>
                  </div>
                }
              }
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Expandable mode body -->
      <ng-template pTemplate="body" let-data *ngIf="config.mode === 'expandable'">
        <tr>
          <td class="expander-column">
            <a href="#" (click)="toggleRow(data, $event)">
              <i class="pi" [ngClass]="{'pi-chevron-down': isRowExpanded(data), 'pi-chevron-right': !isRowExpanded(data)}"></i>
            </a>
          </td>
          <td>{{ data.id }}</td>
        </tr>
      </ng-template>

      <!-- Expandable mode row expansion -->
      <ng-template pTemplate="rowexpansion" let-item *ngIf="config.mode === 'expandable'">
        <tr>
          <td colspan="2">
            <p-table [value]="item.locales" [scrollable]="true" dataKey="index">
              <ng-template pTemplate="header">
                <tr>
                  <th>{{ "domainValuesManagement.changeHistory.modalDetails.language" | translate }}</th>
                  <th>{{ "domainValuesManagement.changeHistory.modalDetails.operationType" | translate }}</th>
                  <th>{{ "domainValuesManagement.changeHistory.modalDetails.oldValue" | translate }}</th>
                  <th>{{ "domainValuesManagement.changeHistory.modalDetails.newValue" | translate }}</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-element>
                <tr>
                  <td>{{ element.language }}</td>
                  <td><p-tag [severity]="getSeverity(element.operationType)" [value]="element.operationType"></p-tag></td>
                  <td>{{ element.oldValue }}</td>
                  <td>{{ element.newValue }}</td>
                </tr>
              </ng-template>
            </p-table>
          </td>
        </tr>
      </ng-template>
    </p-table>
  `,
  standalone: true,
  imports: [
    TableModule,
    TagModule,
    CommonModule,
    TranslateModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./changes-table.component.scss']
})
export class ChangesTableComponent implements OnInit, OnChanges {
  @Input() data: ChangesTableData[] | DomainValueChangesRequestItem[] = [];
  @Input() config: ChangesTableConfig = { mode: 'summary' };

  processedData: any[] = [];
  expandedRows: Set<any> = new Set();
  protected readonly tabType = ManagementValuesTabType;
  protected readonly operationTypeStatus = OperationTypes;

  ngOnInit() {
    this.processData();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['data'] || changes['config']) {
      this.processData();
    }
  }

  toggleRow(data: any, event: Event) {
    if (this.expandedRows.has(data)) {
      this.expandedRows.delete(data);
    } else {
      this.expandedRows.add(data);
    }
    event.preventDefault();
  }

  isRowExpanded(data: any): boolean {
    return this.expandedRows.has(data);
  }

  getSeverity(value: string): "success" | "secondary" | "info" | "warning" | "danger" | "contrast" {
    switch (value) {
      case OperationTypes.ADD:
        return 'success';
      case OperationTypes.UPDATE:
        return 'warning';
      case OperationTypes.DELETE:
        return 'danger';
      default:
        return 'info';
    }
  }

  private processData() {
    if (this.config.mode === 'summary') {
      this.processedData = this.data as DomainValueChangesRequestItem[];
    } else {
      // Transform ChangeDetailsComponent data format to match table expectations
      this.processedData = this.transformDetailsData(this.data as ChangesTableData[]);
    }
  }

  private transformDetailsData(data: ChangesTableData[]): any[] {
    // Transform the data from ChangeDetailsComponent format to table format
    if (!data || data.length === 0) return [];
    
    const firstItem = data[0];
    if (firstItem.valuesChanges) {
      return firstItem.valuesChanges;
    }
    
    return [];
  }
}
