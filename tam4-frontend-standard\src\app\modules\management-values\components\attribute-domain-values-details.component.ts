import { CommonModule } from "@angular/common";
import {Component, Input, OnD<PERSON>roy, OnInit, signal, computed, effect} from '@angular/core';
import { FormsModule } from "@angular/forms";
import { SelectorMap } from '@creactives/models';
import { Tam4TranslationModule, Tam4TranslationService } from '@creactives/tam4-translation-core';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from "primeng/inputtext";
import { PanelModule } from 'primeng/panel';
import { TableModule } from "primeng/table";
import { TagModule } from "primeng/tag";
import { ToastModule } from "primeng/toast";
import { TooltipModule } from 'primeng/tooltip';
import { Tam4SelectorConfig, TamAbstractReduxComponent } from 'src/app/components';
import { DomainValuesStatusTransformPipe } from "../common/status-transform.pipe";
import { ManagementValuesService } from '../management-values.service';
import {
  OperationTypes,
  DomainValue,
  DomainValueAttributeColumn,
  DomainValueAttributeUpdate,
  DomainValues,
  TreeDomainAttribute
} from '../models/management-values.types';

const storeSelectors: Tam4SelectorConfig[] = [];

@Component({
  selector: 'div[attributeDomainValuesDetails]',
  template: `
    <div class="values-details-component">
      @if (valuesAttributes()) {
        <div>
          <p-table
              #dt1
              [value]="valuesAttributes()"
              dataKey="id"
              [editMode]="isEditable() ? 'cell' : null"
              [scrollable]="true"
              scrollHeight="500px"
              [frozenWidth]="'200px'"
              styleClass="w-100 p-datatable-compat p-datatable-striped mt-3 mb-3"
              [globalFilterFields]="globalFilterFields()"
              [tableStyle]="{ 'min-width': '50rem' }"
              sortField="id"
              [sortOrder]="1"
          >
            <ng-template pTemplate="caption">
              <div style="text-align: left">
                <span class="p-input-icon-left" style="width: 100%; position: relative;">
                  <i class="pi pi-search"></i>
                  <input #searchInput
                         pInputText
                         type="text"
                         [(ngModel)]="searchValue"
                         (input)="onSearchInputChange(searchInput.value, dt1)"
                         placeholder="{{ 'domainValuesManagement.domainValues.searchPlaceholder' | translate }}"
                         style="padding-left: 2.2rem;"/>
                </span>
              </div>
            </ng-template>
            <ng-template pTemplate="header">
              <tr>
                <th style="min-width: 200px; box-shadow: inset 0 0 0 #ccc;"
                    pFrozenColumn
                    pSortableColumn="id">
                  {{ 'domainValuesManagement.domainValues.columnValues' | translate }}
                  <p-sortIcon field="id"/>
                </th>
                @for (column of columnLanguages; track column.key; let i = $index) {
                  <th style="min-width:100px"
                      [pSortableColumn]="getValueFieldName(i)">
                    {{ column.key | countryTranslate }}
                    <p-sortIcon [field]="getValueFieldName(i)"/>
                  </th>
                }
                <th style="width:15%">
                  {{ 'domainValuesManagement.domainValues.status' | translate }}
                </th>
                @if (isEditable()) {
                  <th style="width:10%" alignFrozen="right" pFrozenColumn [frozen]="true">
                    {{ 'domainValuesManagement.domainValues.actions' | translate }}
                  </th>
                }
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-rowData let-index="rowIndex">
              <tr [ngClass]="getRowClasses(rowData)">
                <td pFrozenColumn>{{ rowData.id }}</td>
                @for (lang of rowData.values; track lang.key; let i = $index) {
                  <td [pEditableColumn]="canEditCell(rowData)">
                    <p-cellEditor>
                      <ng-template pTemplate="input">
                        <input
                            pInputText
                            type="text"
                            [(ngModel)]="lang.newValue"
                            (keydown.enter)="onCellValueChange(rowData, lang, $event)"
                            (blur)="onCellValueChange(rowData, lang, $event)"
                            [disabled]="!canEditCell(rowData)"
                            style="width: 100%"
                        />
                      </ng-template>
                      <ng-template pTemplate="output">
                        <span [ngClass]="getValueClasses(lang)">
                          {{ lang.newValue }}
                        </span>
                      </ng-template>
                    </p-cellEditor>
                  </td>
                }
                <td>
                  @if (rowData.status?.trim()) {
                    <p-tag [value]="rowData.status"
                           [severity]="rowData.status | domainValuesStatusTransform">
                    </p-tag>
                  }
                </td>
                @if (isEditable()) {
                  <td alignFrozen="right" pFrozenColumn [frozen]="true" class="text-center">
                    @if (!isRowDeleted(rowData)) {
                      <button type="button"
                              (click)="onButtonDelete(rowData)"
                              class="p-button p-button-text p-0 border-0 bg-transparent action-button delete-button"
                              pTooltip="{{ 'domainValuesManagement.domainValues.delete' | translate }}"
                              tooltipPosition="top">
                        <i class="pi pi-trash"></i>
                      </button>
                    } @else {
                      <button type="button"
                              (click)="onButtonRestore(rowData)"
                              class="p-button p-button-text p-0 border-0 bg-transparent action-button restore-button"
                              pTooltip="{{ 'domainValuesManagement.domainValues.restore' | translate }}"
                              tooltipPosition="top">
                        <i class="pi pi-undo"></i>
                      </button>
                    }
                  </td>
                }
              </tr>
            </ng-template>
            @if (isEditable()) {
              <ng-template pTemplate="footer" class="mb-3">
                  <tr class="mb-3">
                    <td pFrozenColumn class="p-1">
                      <input
                          type="text"
                          [(ngModel)]="newRowData().id"
                          class="p-inputtext p-component"
                          placeholder="{{ 'domainValuesManagement.domainValues.columnValues' | translate }}"
                          [attr.aria-label]="'domainValuesManagement.domainValues.columnValues' | translate"
                          (keydown.enter)="onAddNewRow()" />
                    </td>
                    @for (column of columnLanguages; track column.key; let i = $index) {
                      <td class="p-1">
                        <input
                            type="text"
                            class="p-inputtext p-component"
                            [ngModel]="getNewRowValue(i)"
                            (ngModelChange)="setNewRowValue(i, $event)"
                            placeholder="{{ column.key | countryTranslate }}"
                            [attr.aria-label]="column.key | countryTranslate"
                            (keydown.enter)="onAddNewRow()" />
                      </td>
                    }
                    <td></td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true">
                      <div class="flex align-items-center justify-content-center gap-2">
                        <button
                            type="button"
                            (click)="onAddNewRow()"
                            class="p-button p-button-text p-0 border-0 bg-transparent save-button"
                            pTooltip="{{ 'domainValuesManagement.domainValues.save' | translate }}"
                            tooltipPosition="top">
                          <i class="pi pi-check"></i>
                        </button>
                        <button
                            type="button"
                            (click)="onCancelNewRow()"
                            class="p-button p-button-text p-0 border-0 bg-transparent cancel-button"
                            pTooltip="{{ 'domainValuesManagement.domainValues.cancel' | translate }}"
                            tooltipPosition="top">
                          <i class="pi pi-times"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
              </ng-template>
            }
          </p-table>
        </div>
      }
    </div>
  `,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    CardModule,
    InputTextModule,
    ButtonModule,
    TranslateModule,
    TagModule,
    ToastModule,
    TooltipModule,
    Tam4TranslationModule,
    PanelModule,
    DomainValuesStatusTransformPipe
  ],
  styleUrls: ['./attribute-domain-values-details.component.scss'],
  host: {
    '[class]': '\'values-details-component\''
  }
})
export class AttributeDomainValuesDetailsComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit, OnDestroy {

  constructor(
      protected translate: TranslateService,
      protected tamTranslate: Tam4TranslationService,
      protected store: Store,
      private service: ManagementValuesService,
      private messageService: MessageService
  ) {
    super(translate, tamTranslate, store, storeSelectors);
  }

  @Input() columnLanguages: DomainValueAttributeColumn[] = [];
  @Input() valuesAttributes = signal<DomainValues[]>([]);
  @Input() attribute = signal<TreeDomainAttribute>(null);

  searchValue = signal<string>('');
  newRowData = signal<DomainValues>(this.createEmptyRow());

  globalFilterFields = computed(() => {
    if (!this.columnLanguages || this.columnLanguages.length === 0) {
      return ['id', 'status'];
    }

    return [
      'id',
      'status',
      ...this.columnLanguages.map((_, index) => `values.${index}.newValue`),
      ...this.columnLanguages.map((_, index) => `values.${index}.oldValue`)
    ];
  });

  isEditable = computed(() => this.attribute()?.editable || false);

  isNewRowValid = computed(() => {
    const newRow = this.newRowData();
    const trimmedId = newRow.id?.trim();

    const hasValidBasicData = !!(trimmedId && newRow.values?.every(v => v.newValue?.trim()));

    if (!hasValidBasicData) {
      return false;
    }

    const existingValues = this.valuesAttributes() || [];
    const isDuplicateId = existingValues.some(existingRow =>
      existingRow.id === trimmedId && !this.isRowDeleted(existingRow)
    );

    return !isDuplicateId;
  });

  private attributeChangeEffect = effect(() => {
    const currentAttribute = this.attribute();
    if (currentAttribute) {
      this.searchValue.set('');
      this.resetNewRow();
    }
  }, { allowSignalWrites: true });

  protected readonly OperationTypes = OperationTypes;

  ngOnInit() {
    super.ngOnInit();
    this.initializeComponent();
  }

  private initializeComponent(): void {
    if (this.attribute()) {
      this.resetNewRow();
    }
  }

  getValueFieldName(index: number): string {
    return `values.${index}.newValue`;
  }

  getRowClasses(rowData: DomainValues): Record<string, boolean> {
    return {
      'row-deleted': this.isRowDeleted(rowData),
      'row-added': this.isRowAdded(rowData)
    };
  }

  getValueClasses(lang: DomainValue): Record<string, boolean> {
    return {
      'modified-value': lang.newValue !== lang.oldValue
    };
  }

  canEditCell(rowData: DomainValues): boolean {
    return this.isEditable() && !this.isRowDeleted(rowData);
  }

  getNewRowValue(index: number): string {
    return this.newRowData().values[index]?.newValue || '';
  }

  setNewRowValue(index: number, value: string): void {
    const row = this.newRowData();
    if (row.values[index]) {
      row.values[index].newValue = value;
      this.newRowData.set({ ...row, values: [...row.values] });
    }
  }

  onSearchInputChange(value: string, table: any): void {
    this.searchValue.set(value);
    table.filterGlobal(value, 'contains');
  }

  onCellValueChange(rowData: DomainValues, valueLang: DomainValue, event?: Event): void {
    if (event && (event as KeyboardEvent).key === 'Enter') {
      event.preventDefault();
    }

    if (valueLang.newValue !== valueLang.oldValue) {
      this.updateRowValue(rowData, valueLang);
    }
  }

  private updateRowValue(rowData: DomainValues, valueLang: DomainValue): void {
    valueLang.edited = true;
    rowData.previousStatus = rowData.status;

    if (rowData.status !== OperationTypes.ADD) {
      rowData.status = OperationTypes.UPDATE;
    }

    rowData.editable = true;
    this.notifyValueUpdate(rowData);
  }

  onAddNewRow(): void {
    if (!this.isNewRowValid()) {
      this.showError('domainValuesManagement.domainValues.errorValidatingAdd');
      return;
    }

    const newRow = this.createNewRowFromInput();
    this.addRowToTable(newRow);
    this.notifyValueUpdate(newRow);
    this.resetNewRow();
  }

  onCancelNewRow(): void {
    this.resetNewRow();
  }

  onButtonDelete(rowDelete: DomainValues): void {
    if (!this.attribute()) { return; }

    if (rowDelete.status === OperationTypes.ADD) {
      this.removeRowCompletely(rowDelete);
      return;
    }

    this.service.action_doValidateDeleteAttributeValue(
        this.attribute().key,
        rowDelete.id,
        this.attribute().parentId,
        (isUsed: boolean) => this.handleDeleteValidation(rowDelete, isUsed),
        (error: any) => this.showError('domainValuesManagement.domainValues.errorValidatingDelete')
    );
  }

  onButtonRestore(rowRestore: DomainValues): void {
    rowRestore.status = rowRestore.previousStatus;
    this.notifyValueUpdate(rowRestore);
  }

  private createEmptyRow(): DomainValues {
    return {
      id: '',
      status: null,
      values: this.columnLanguages?.map(column => ({
        key: column.key,
        oldValue: null,
        newValue: null,
        edited: false
      })) || [],
      editable: true
    };
  }

  private resetNewRow(): void {
    this.newRowData.set(this.createEmptyRow());
  }

  private createNewRowFromInput(): DomainValues {
    const inputRow = this.newRowData();
    inputRow.values.forEach(value => value.edited = true);

    return {
      id: inputRow.id,
      status: OperationTypes.ADD,
      values: [...inputRow.values],
      editable: true
    };
  }

  private addRowToTable(newRow: DomainValues): void {
    const currentValues = this.valuesAttributes() || [];
    this.valuesAttributes?.set([...currentValues, newRow]);
  }

  private removeRowCompletely(rowToRemove: DomainValues): void {
    const currentValues = this.valuesAttributes() || [];
    const updatedValues = currentValues.filter(row => row.id !== rowToRemove.id);
    this.valuesAttributes?.set(updatedValues);
    this.notifyAddRowRemoval(rowToRemove);
  }

  private notifyAddRowRemoval(rowData: DomainValues): void {
    if (!this.attribute()) { return; }

    const removalUpdate: DomainValueAttributeUpdate = {
      attributeId: this.attribute().key,
      valueId: rowData.id,
      values: [],
      domainValues: [],
      status: OperationTypes.DELETE,
      previousStatus: OperationTypes.ADD
    };

    this.service.action_doUpdateDomainValues(removalUpdate);
  }

  private handleDeleteValidation(rowDelete: DomainValues, isUsed: boolean): void {
    if (isUsed) {
      this.showError('domainValuesManagement.domainValues.cannotDeleteValue');
      return;
    }

    rowDelete.previousStatus = rowDelete.status;
    rowDelete.status = OperationTypes.DELETE;
    this.notifyValueUpdate(rowDelete);
  }

  private notifyValueUpdate(rowData: DomainValues): void {
    if (!this.attribute()) { return; }

    const updatedValue: DomainValueAttributeUpdate = {
      attributeId: this.attribute().key,
      valueId: rowData.id,
      values: this.valuesAttributes().filter(val => val.id === rowData.id),
      domainValues: rowData.values.map(lang => ({
        key: lang.key,
        oldValue: lang.oldValue,
        newValue: lang.newValue,
        edited: lang.edited
      })),
      status: rowData.status,
      previousStatus: rowData.previousStatus
    };

    this.service.action_doUpdateDomainValues(updatedValue);
    if (rowData.status !== OperationTypes.UPDATE) {
      this.showSuccess('domainValuesManagement.domainValues.valueUpdated.' + rowData.status.toLowerCase());
    }
  }

  private showMessage(severity: 'success' | 'error' | 'info' | 'warn', messageKey: string): void {
    const summaryKey = severity === 'success'
      ? 'domainValuesManagement.messages.success'
      : 'domainValuesManagement.messages.error';

    this.messageService.add({
      severity,
      summary: this.translate.instant(summaryKey),
      detail: this.translate.instant(messageKey)
    });
  }

  private showError(messageKey: string): void {
    this.showMessage('error', messageKey);
  }

  private showSuccess(messageKey: string): void {
    this.showMessage('success', messageKey);
  }

  isRowDeleted(rowData: DomainValues): boolean {
    return rowData.status === OperationTypes.DELETE;
  }

  isRowAdded(rowData: DomainValues): boolean {
    return rowData.status === OperationTypes.ADD;
  }

  private isRowModified(rowData: DomainValues): boolean {
    return rowData.values?.some(val => val.edited) || false;
  }

  ngOnDestroy() {
    super.ngOnDestroy();
  }
}
